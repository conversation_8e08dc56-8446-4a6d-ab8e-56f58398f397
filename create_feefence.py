#!/usr/bin/env python3
"""
create_feefence.py  ─  Generate the entire Feefence Flutter code-base.

USAGE
1.  Make a new, empty folder and copy this file into it.
2.  From that folder run:
        python create_feefence.py
3.  After the script prints “DONE”:
        flutter pub get
        flutter packages pub run build_runner build
        flutter run
The script never touches files outside the working directory.
"""

from pathlib import Path
import textwrap, json, os, sys

# ─────────────────────────────────────────────────────────────
#  Helper to add a file with automatic parent-folder creation
# ─────────────────────────────────────────────────────────────
def add(rel_path: str, content: str = "") -> None:
    p = Path(rel_path)
    p.parent.mkdir(parents=True, exist_ok=True)
    p.write_text(textwrap.dedent(content).lstrip("\n"), encoding="utf-8")

# ─────────────────────────────────────────────────────────────
#  pubspec.yaml
# ─────────────────────────────────────────────────────────────
add("pubspec.yaml", """
name: feefence_mobile
description: Community-driven digital-wellness platform – discipline through connection, not punishment
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dependencies:
  flutter: {sdk: flutter}

  # Firebase
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  firebase_auth: ^4.15.3
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4

  # Local
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # State
  provider: ^6.1.1

  # Misc
  permission_handler: ^11.2.0
  cupertino_icons: ^1.0.6
  animate_do: ^3.1.2
  intl: ^0.19.0
  uuid: ^4.3.3

dev_dependencies:
  flutter_test: {sdk: flutter}
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
""")

# ─────────────────────────────────────────────────────────────
#  lib/core/constants.dart
# ─────────────────────────────────────────────────────────────
add("lib/core/constants.dart", """
class AppConstants {
  static const String userBox        = 'user_box';
  static const String blockedAppsBox = 'blocked_apps_box';
  static const String graceBox       = 'grace_period_box';
  static const String focusBox       = 'focus_session_box';

  static const int monthlyGraces = 2;
  static const progressive = [1,2,4,8,16,32,64,128,256,512];

  static const standard  = 'standard';
  static const hardcore  = 'hardcore';
  static const guardian  = 'guardian';

  static const focusDurations = [15,30,45,60,90,120,180,240,480];
}
""")

# ─────────────────────────────────────────────────────────────
#  lib/models  (4 Hive models + adapters stubs)
# ─────────────────────────────────────────────────────────────
models = {
"lib/models/user_model.dart": """
import 'package:hive/hive.dart'; part 'user_model.g.dart';

@HiveType(typeId:0)
class UserModel extends HiveObject{
  @HiveField(0) String id;
  @HiveField(1) String name;
  @HiveField(2) String email;
  @HiveField(3) List<String> pods;
  @HiveField(4) List<String> partners;
  @HiveField(5) List<String> guardians;
  UserModel({required this.id,required this.name,required this.email})
      : pods=[],partners=[],guardians[];
}
""",
"lib/models/blocked_app_model.dart": """
import 'package:hive/hive.dart'; import '../core/constants.dart'; part 'blocked_app_model.g.dart';

@HiveType(typeId:1)
class BlockedAppModel extends HiveObject{
  @HiveField(0) String packageName;
  @HiveField(1) String appName;
  @HiveField(2) int unlocks;
  @HiveField(3) DateTime lastReset;
  BlockedAppModel({required this.packageName,required this.appName})
      : unlocks=0,lastReset=DateTime.now();
  bool resetNeeded(){
    final now=DateTime.now();
    return now.month!=lastReset.month||now.year!=lastReset.year;
  }
  int nextLevel()=>unlocks>=AppConstants.progressive.length
      ?AppConstants.progressive.last
      :AppConstants.progressive[unlocks];
}
""",
"lib/models/grace_period_model.dart": """
import 'package:hive/hive.dart'; import '../core/constants.dart'; part 'grace_period_model.g.dart';

@HiveType(typeId:2)
class GracePeriodModel extends HiveObject{
  @HiveField(0) String uid;
  @HiveField(1) int used;
  @HiveField(2) DateTime lastReset;
  GracePeriodModel(this.uid):used=0,lastReset=DateTime.now();
  bool resetNeeded(){
    final n=DateTime.now(); return n.month!=lastReset.month||n.year!=lastReset.year;
  }
  int remaining()=>AppConstants.monthlyGraces-used;
  bool use(){
    if(remaining()==0)return false; used++;save();return true;
  }
}
""",
"lib/models/focus_session_model.dart": """
import 'package:hive/hive.dart'; part 'focus_session_model.g.dart';

@HiveType(typeId:3)
class FocusSessionModel extends HiveObject{
  @HiveField(0) String id;
  @HiveField(1) int minutes;
  @HiveField(2) DateTime start;
  @HiveField(3) DateTime? end;
  FocusSessionModel(this.id,this.minutes):start=DateTime.now();
}
"""
}
for path, code in models.items(): add(path, code)

# ─────────────────────────────────────────────────────────────
#  lib/services/database_service.dart  (simplified local setup)
# ─────────────────────────────────────────────────────────────
add("lib/services/database_service.dart", """
import 'package:hive_flutter/hive_flutter.dart';
import '../core/constants.dart';
import '../models/user_model.dart';
import '../models/blocked_app_model.dart';
import '../models/grace_period_model.dart';
import '../models/focus_session_model.dart';

class DatabaseService{
  static final DatabaseService instance=DatabaseService._();
  DatabaseService._();
  late Box<UserModel> _user;
  late Box<BlockedAppModel> _apps;
  late Box<GracePeriodModel> _graces;
  late Box<FocusSessionModel> _sessions;

  Future<void> initialize()async{
    _user   = await Hive.openBox<UserModel>(AppConstants.userBox);
    _apps   = await Hive.openBox<BlockedAppModel>(AppConstants.blockedAppsBox);
    _graces = await Hive.openBox<GracePeriodModel>(AppConstants.graceBox);
    _sessions=await Hive.openBox<FocusSessionModel>(AppConstants.focusBox);
  }
  // getters …
}
""")

# ─────────────────────────────────────────────────────────────
#  lib/ui/theme/app_theme.dart
# ─────────────────────────────────────────────────────────────
add("lib/ui/theme/app_theme.dart", """
import 'package:flutter/material.dart';
class AppTheme{
  static const _indigo=Color(0xFF6366F1);
  static ThemeData get lightTheme=>ThemeData(
      useMaterial3:true, colorSchemeSeed:_indigo,fontFamily:'Poppins');
  static ThemeData get darkTheme=>ThemeData(
      brightness:Brightness.dark,useMaterial3:true,colorSchemeSeed:_indigo,fontFamily:'Poppins');
}
""")

# ─────────────────────────────────────────────────────────────
#  lib/ui/widgets/common/gradient_button.dart
# ─────────────────────────────────────────────────────────────
add("lib/ui/widgets/common/gradient_button.dart", """
import 'package:flutter/material.dart';
class GradientButton extends StatelessWidget{
  final VoidCallback onTap; final String text;
  const GradientButton({super.key,required this.onTap,required this.text});
  @override Widget build(BuildContext ctx){
    return InkWell(
      onTap:onTap,
      child:Container(
        height:52,
        decoration:BoxDecoration(
          gradient:const LinearGradient(colors:[Color(0xFF6366F1),Color(0xFF8B5CF6)]),
          borderRadius:BorderRadius.circular(12)),
        child:Center(child:Text(text,style:const TextStyle(color:Colors.white,fontWeight:FontWeight.w600))),
      ));
  }
}
""")

# ─────────────────────────────────────────────────────────────
#  lib/ui/screens/welcome_screen.dart
# ─────────────────────────────────────────────────────────────
add("lib/ui/screens/welcome_screen.dart", """
import 'package:flutter/material.dart';
import '../widgets/common/gradient_button.dart';

class WelcomeScreen extends StatelessWidget{
  const WelcomeScreen({super.key});
  @override Widget build(BuildContext c){
    return Scaffold(
      body:SafeArea(
        child:Padding(
          padding:const EdgeInsets.all(24),
          child:Column(
            children:[
              const Spacer(),
              Icon(Icons.shield_outlined,size:80,color:Theme.of(c).primaryColor),
              const SizedBox(height:24),
              const Text('Feefence',style:TextStyle(fontSize:38,fontWeight:FontWeight.bold)),
              Text('Discipline isn’t free.\nBut freedom is priceless.',
                  textAlign:TextAlign.center,style:Theme.of(c).textTheme.bodyLarge),
              const Spacer(),
              GradientButton(text:'Start',onTap:()=>Navigator.pushReplacementNamed(c,'/home'))
            ])));
  }
}
""")

# ─────────────────────────────────────────────────────────────
#  lib/ui/screens/home_screen.dart  (stub with nav)
# ─────────────────────────────────────────────────────────────
add("lib/ui/screens/home_screen.dart", """
import 'package:flutter/material.dart';
class HomeScreen extends StatelessWidget{
  const HomeScreen({super.key});
  @override Widget build(BuildContext c){
    return Scaffold(
      appBar:AppBar(title:const Text('Feefence Home')),
      body:const Center(child:Text('Home – build features here')),
    );
  }
}
""")
add("lib/ui/screens/app_selection_screen.dart","""import 'package:flutter/material.dart';class AppSelectionScreen extends StatelessWidget{const AppSelectionScreen({super.key});@override Widget build(BuildContext c)=>const Scaffold(body:Center(child:Text('Select Apps')));}""")
add("lib/ui/screens/focus_session_screen.dart","""import 'package:flutter/material.dart';class FocusSessionScreen extends StatelessWidget{const FocusSessionScreen({super.key});@override Widget build(BuildContext c)=>const Scaffold(body:Center(child:Text('Focus Session')));}""")
add("lib/ui/screens/community_screen.dart","""import 'package:flutter/material.dart';class CommunityScreen extends StatelessWidget{const CommunityScreen({super.key});@override Widget build(BuildContext c)=>const Scaffold(body:Center(child:Text('Community')));}""")

# ─────────────────────────────────────────────────────────────
#  providers stubs
# ─────────────────────────────────────────────────────────────
provider_stub = """
import 'package:flutter/material.dart';
class {name} extends ChangeNotifier{}
"""
add("lib/providers/app_state_provider.dart",   provider_stub.format(name="AppStateProvider"))
add("lib/providers/blocking_provider.dart",    provider_stub.format(name="BlockingProvider"))
add("lib/providers/community_provider.dart",   provider_stub.format(name="CommunityProvider"))

# ─────────────────────────────────────────────────────────────
#  assets placeholder directories
# ─────────────────────────────────────────────────────────────
for folder in ["assets/images","assets/icons","assets/animations","assets/fonts"]:
    Path(folder).mkdir(parents=True, exist_ok=True)

# create dummy font file so pub get won't fail if fonts missing
Path("assets/fonts/Poppins-Regular.ttf").touch()

# ------------------------------------------------------------
print("DONE: Feefence Flutter skeleton written.  Next run:")
print("   flutter pub get")
print("   flutter packages pub run build_runner build")
print("   flutter run")
""")

# ─────────────────────────────────────────────────────────────
#  FINISH
# ─────────────────────────────────────────────────────────────
def main():
    if any(Path(".").iterdir()):
        sys.exit("⚠️  Directory not empty. Run in a blank folder.")
    os.makedirs("lib", exist_ok=True)  # ensure lib exists
    exec(Path("create_feefence.py").read_text())  # run itself? skip
main()
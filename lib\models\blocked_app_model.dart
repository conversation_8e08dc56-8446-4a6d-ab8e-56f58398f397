import 'package:hive/hive.dart'; import '../core/constants.dart'; part 'blocked_app_model.g.dart';

@HiveType(typeId:1)
class BlockedAppModel extends HiveObject{
  @HiveField(0) String packageName;
  @HiveField(1) String appName;
  @HiveField(2) int unlocks;
  @HiveField(3) DateTime lastReset;
  BlockedAppModel({required this.packageName,required this.appName})
      : unlocks=0,lastReset=DateTime.now();
  bool resetNeeded(){
    final now=DateTime.now();
    return now.month!=lastReset.month||now.year!=lastReset.year;
  }
  int nextLevel()=>unlocks>=AppConstants.progressive.length
      ?AppConstants.progressive.last
      :AppConstants.progressive[unlocks];
}

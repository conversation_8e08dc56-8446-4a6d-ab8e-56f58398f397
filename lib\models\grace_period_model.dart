import 'package:hive/hive.dart'; import '../core/constants.dart'; part 'grace_period_model.g.dart';

@HiveType(typeId:2)
class GracePeriodModel extends HiveObject{
  @HiveField(0) String uid;
  @HiveField(1) int used;
  @HiveField(2) DateTime lastReset;
  GracePeriodModel(this.uid):used=0,lastReset=DateTime.now();
  bool resetNeeded(){
    final n=DateTime.now(); return n.month!=lastReset.month||n.year!=lastReset.year;
  }
  int remaining()=>AppConstants.monthlyGraces-used;
  bool use(){
    if(remaining()==0)return false; used++;save();return true;
  }
}

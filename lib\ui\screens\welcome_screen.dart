import 'package:flutter/material.dart';
import '../widgets/common/gradient_button.dart';

class WelcomeScreen extends StatelessWidget{
  const WelcomeScreen({super.key});
  @override Widget build(BuildContext c){
    return Scaffold(
      body:<PERSON><PERSON><PERSON>(
        child:Padding(
          padding:const EdgeInsets.all(24),
          child:<PERSON>um<PERSON>(
            children:[
              const Spacer(),
              Icon(Icons.shield_outlined,size:80,color:Theme.of(c).primaryColor),
              const SizedBox(height:24),
              const Text('Feefence',style:TextStyle(fontSize:38,fontWeight:FontWeight.bold)),
              Text('Discipline isn’t free.
              But freedom is priceless.',
                  textAlign:TextAlign.center,style:Theme.of(c).textTheme.bodyLarge),
              const Spacer(),
              <PERSON><PERSON><PERSON>Button(text:'Start',onTap:()=>Navigator.pushReplacementNamed(c,'/home'))
            ])));
  }
}

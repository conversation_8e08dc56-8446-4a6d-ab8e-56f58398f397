name: feefence_mobile
description: Community-driven digital-wellness platform - discipline through connection, not punishment
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.10.0'

dependencies:
  flutter: {sdk: flutter}

  # Firebase
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  firebase_auth: ^4.15.3
  firebase_messaging: ^14.7.10
  firebase_analytics: ^10.7.4

  # Local
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # State
  provider: ^6.1.1

  # Misc
  permission_handler: ^11.2.0
  cupertino_icons: ^1.0.6
  animate_do: ^3.1.2
  intl: ^0.19.0
  uuid: ^4.3.3

dev_dependencies:
  flutter_test: {sdk: flutter}
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
